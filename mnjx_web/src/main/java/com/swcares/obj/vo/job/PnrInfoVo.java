package com.swcares.obj.vo.job;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class PnrInfoVo {
    /**
     * 查询pnr数据需要的集合
     */
    List<String> tkList = new ArrayList<>();
    List<String> pnrSegIdList = new ArrayList<>();
    List<String> pnrOsiIdList = new ArrayList<>();
    List<String> pnrGnIdList = new ArrayList<>();
    List<String> pnrFcIdList = new ArrayList<>();
    List<String> pnrEiIdList = new ArrayList<>();
    List<String> pnrTcIdList = new ArrayList<>();
    List<String> pnrAtList = new ArrayList<>();
    List<String> pnrFnIdList = new ArrayList<>();
    List<String> pnrFpIdList = new ArrayList<>();
    List<String> pnrRecordIdList = new ArrayList<>();
    List<String> pnrRmkIdList = new ArrayList<>();
    List<String> pnrCtIdList = new ArrayList<>();
    List<String> pnrSubIdList = new ArrayList<>();
    List<String> idBgtList = new ArrayList<>();
    List<String> pnrIdsList = new ArrayList<>();
}
