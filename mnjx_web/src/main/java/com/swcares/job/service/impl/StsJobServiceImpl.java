package com.swcares.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.core.util.ThreadUtils;
import com.swcares.entity.*;
import com.swcares.job.service.IStsJobService;
import com.swcares.mapper.StsJobMapper;
import com.swcares.obj.vo.PlanFlightNoDateVo;
import com.swcares.obj.vo.excel.PassengerExcelVo;
import com.swcares.obj.vo.job.FltAndPsgVo;
import com.swcares.obj.vo.job.NmInfoVo;
import com.swcares.obj.vo.job.PnrInfoVo;
import com.swcares.obj.vo.job.SeatVo;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StsJobServiceImpl implements IStsJobService {
    /**
     * 执行任务的线程池
     * 当线程数小于核心线程数时，创建线程。
     * 当线程数大于等于核心线程数，且任务队列未满时，将任务放入任务队列。
     * 当线程数大于等于核心线程数，且任务队列已满，若线程数小于最大线程数，创建线程。
     * 若线程数等于最大线程数，则执行拒绝策略
     */
    private final static ThreadPoolTaskExecutor THREAD_POOL_TASK_EXECUTOR_FLIGHT = ThreadUtils.getThreadPoolTaskExecutor("航班生效job任务-");

    /**
     * 执行任务的线程池
     * 当线程数小于核心线程数时，创建线程。
     * 当线程数大于等于核心线程数，且任务队列未满时，将任务放入任务队列。
     * 当线程数大于等于核心线程数，且任务队列已满，若线程数小于最大线程数，创建线程。
     * 若线程数等于最大线程数，则执行拒绝策略
     */
    private final static ThreadPoolTaskExecutor THREAD_POOL_TASK_EXECUTOR_PSG = ThreadUtils.getThreadPoolTaskExecutor("生成旅客的线程池-");
    /**
     * 执行任务的线程池
     * 当线程数小于核心线程数时，创建线程。
     * 当线程数大于等于核心线程数，且任务队列未满时，将任务放入任务队列。
     * 当线程数大于等于核心线程数，且任务队列已满，若线程数小于最大线程数，创建线程。
     * 若线程数等于最大线程数，则执行拒绝策略
     */
    private final static ThreadPoolTaskExecutor THREAD_POOL_TASK_EXECUTOR_CLEAR = ThreadUtils.getThreadPoolTaskExecutor("清理数据的线程池-");

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IPassengerService iPassengerService;

    @Resource
    private IFlightService iFlightService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxFlightScreenService iMnjxFlightScreenService;

    @Resource
    private StsJobMapper stsJobMapper;

    @Resource
    private IPlanFlightService iPlanFlightService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private IMnjxLuggageUnmannedService iMnjxLuggageUnmannedService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxPnrGnService iMnjxPnrGnService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxMentService iMnjxMentService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxLuggageCarryonService iMnjxLuggageCarryonService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxExLuggageService iMnjxExLuggageService;

    @Resource
    private IMnjxPsgCkiOptionService iMnjxPsgCkiOptionService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;

    @Resource
    private IMnjxVerifyInfoService iMnjxVerifyInfoService;

    @Resource
    private IMnjxUnpackInfoService iMnjxUnpackInfoService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxRefundTicketService iMnjxRefundTicketService;

    @Resource
    private IMnjxPrintDataService iMnjxPrintDataService;

    @Resource
    private IMnjxJobExecuteService iMnjxJobExecuteService;

    @Resource
    private ITransactionalService iTransactionalService;

    @Resource
    private IMnjxPnrSubService iMnjxPnrSubService;

    @Override
    public void bookingSeats(InputStream inputStream, DateTime startDateTime, DateTime endDateTime) throws IOException {
        // 解析所有的数据，按照sheet的名称为key的格式进行存储
        Map<String, List<PassengerExcelVo>> sortedFinalAllDatas = iPassengerService.preprocessingExcelData(inputStream);
        Stream
                // 获取迭代器
                .iterate(startDateTime, dateTime -> DateUtils.offsetDay(dateTime, Constant.ONE))
                // 到什么地方结束
                .limit(DateUtils.betweenDay(startDateTime, endDateTime, true) + Constant.ONE)
                // 排序，这样执行按照顺序来
                .sorted(DateUtils::compare)
                // 消费数据
                .forEach(dateTime -> THREAD_POOL_TASK_EXECUTOR_PSG.execute(() -> {
                    String flightDate = DateUtils.date2ymd(dateTime);
                    iPassengerService.bookingSeatsForInputStream(sortedFinalAllDatas, flightDate);
                }));
    }

    @Override
    public String[] standardParameterFormatPsg(String param) {
        // 标准参数
        String[] result = new String[3];
        // 获得所有的参数
        String[] args = StrUtils.splitToArray(param, StrUtils.COLON);
        int length = args.length;
        String today = DateUtils.today();
        Date date = new Date();
        int todayWeek = DateUtil.dayOfWeek(date);
        switch (length) {
            // 没有输入参数
            case 0:
                // 默认生成当天至周六的数据
                result[0] = today;
                if (todayWeek == 7) {
                    result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 7).toDateStr();
                } else {
                    result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 7 - todayWeek).toDateStr();
                }
                result[2] = StrUtils.EMPTY;
                break;
            case 1:
                // 只输入了开始时间，生成此时间至7天的数据
                result[0] = StrUtils.isEmpty(args[0]) ? today : args[0];
                if (todayWeek == 7) {
                    result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 7).toDateStr();
                } else {
                    result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 7 - todayWeek).toDateStr();
                }
                result[2] = StrUtils.EMPTY;
                break;
            case 2:
                // 指定开始结束时间
                result[0] = StrUtils.isEmpty(args[0]) ? today : args[0];
                result[1] = StrUtils.isEmpty(args[1]) ? today : args[1];
                result[2] = StrUtils.EMPTY;
                break;
            case 3:
                // 指定开始结束时间并指定旅客源表格
                result[0] = StrUtils.isEmpty(args[0]) ? today : args[0];
                result[1] = StrUtils.isEmpty(args[1]) ? today : args[1];
                result[2] = args[2];
                break;
            default:
                break;
        }
        return result;
    }

    @Override
    public String[] standardParameterFormatFlight(String param) {
        // 标准参数
        String[] result = new String[2];
        // 获得所有的参数
        String[] args = StrUtils.splitToArray(param, StrUtils.COLON);
        int length = args.length;
        String today = DateUtils.today();
        switch (length) {
            // 没有输入参数
            case 0:
                // 当前日期
                result[0] = today;
                // 生成一周的航班 -> 改为生成7*8天的航班
                result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 60).toDateStr();
                break;
            case 1:
                // 输入的开始日期
                result[0] = StrUtils.isEmpty(args[0]) ? today : args[0];
                // 当前日期
                result[1] = DateUtils.offsetDay(DateUtils.parseDate(result[0]), 60).toDateStr();
                break;
            case 2:
                result[0] = StrUtils.isEmpty(args[0]) ? today : args[0];
                result[1] = StrUtils.isEmpty(args[1]) ? today : args[1];
                break;
            default:
                break;
        }
        return result;
    }

    @Override
    public void takeEffect(DateTime startDateTime, DateTime endDateTime) throws UnifiedResultException {
        // 更新航班的开始和结束日期
        log.info("更新tcard数据中的开始日期为:{},结束日期为:{}", DateUtils.date2ymd(startDateTime), DateUtils.date2ymd(endDateTime));
        boolean isOk = iMnjxTcardService.lambdaUpdate()
                .set(MnjxTcard::getStartDate, startDateTime).set(MnjxTcard::getEndDate, endDateTime)
                .update();
        // 所有数据更新成功的条件下
        if (!isOk) {
            throw new UnifiedResultException("tcard数据更新失败，航班生效失败");
        }
        // 获得所有的t-card数据
        List<String> flightIds = iMnjxTcardService.lambdaQuery()
                .select(MnjxTcard::getFlightId)
                .list()
                .stream()
                .map(MnjxTcard::getFlightId)
                .distinct()
                .collect(Collectors.toList());
        // 循环生效每个航班数据
        log.info("===生效数量：{}====", flightIds.size());
        flightIds.forEach(flightId -> THREAD_POOL_TASK_EXECUTOR_FLIGHT.execute(() -> {
            try {
                log.info("====={}生效开始=====", flightId);
                boolean result = iFlightService.takeEffect(flightId);
                if (!result) {
                    log.info("====={}生效失败=====", flightId);
                    throw new UnifiedResultException("航班生效失败");
                }
                log.info("====={}生效结束=====", flightId);
            } catch (UnifiedResultException e) {
                e.printStackTrace();
                log.error("Tcard:{}生效航班发生错误:{}", flightId, e.getMessage());
            }
        }));
    }

    @Override
    public void changeTicketStatus() {
        Set<String> flightNos = iFlightService.retrieveFlightNo();
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery().eq(MnjxPnrNmTicket::getTicketStatus1, Constant.TICKET_STATUS_LIFT_OR_BOARDED).or().eq(MnjxPnrNmTicket::getTicketStatus2, Constant.TICKET_STATUS_LIFT_OR_BOARDED).list();
        if (CollUtil.isNotEmpty(nmTicketList)) {
            String today = DateUtil.today();
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery().in(MnjxPnrSeg::getFlightNo, flightNos).eq(MnjxPnrSeg::getFlightDate, today).list();
            List<String> pnrSegIdList = pnrSegList.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
            List<MnjxPnrNmTicket> nmTicketList1 = nmTicketList.stream().filter(t -> Constant.TICKET_STATUS_LIFT_OR_BOARDED.equals(t.getTicketStatus1()) && pnrSegIdList.contains(t.getS1Id())).collect(Collectors.toList());
            List<MnjxPnrNmTicket> nmTicketList2 = nmTicketList.stream().filter(t -> Constant.TICKET_STATUS_LIFT_OR_BOARDED.equals(t.getTicketStatus2()) && pnrSegIdList.contains(t.getS2Id())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmTicketList1)) {
                nmTicketList1.forEach(t -> t.setTicketStatus1(Constant.TICKET_STATUS_USED_OR_FLOWN));
                iMnjxPnrNmTicketService.updateBatchById(nmTicketList1);
            }
            if (CollUtil.isNotEmpty(nmTicketList2)) {
                nmTicketList2.forEach(t -> t.setTicketStatus2(Constant.TICKET_STATUS_USED_OR_FLOWN));
                iMnjxPnrNmTicketService.updateBatchById(nmTicketList2);
            }
        }
    }

//    private void groupByGate() {
//        ClassPathResource classPathResource = new ClassPathResource("/xls/PassengerDataV8.0.xlsx");
//        InputStream excelFileInputStream = classPathResource.getStream();
//        ExcelReader reader = ExcelUtil.getReader(excelFileInputStream);
//        // 创建工作薄
//        Workbook workbook = reader.getWorkbook();
//        // 获取工作薄里面sheet的个数
//        int sheetNum = workbook.getNumberOfSheets();
//        // 获取有旅客的航班列表
//        List<String> psgFlightNoList = new ArrayList<>();
//        for (int i = 3; i < sheetNum; i++) {
//            Sheet sheet = workbook.getSheetAt(i);
//            String sheetName = sheet.getSheetName();
//            String flightNo = sheetName.substring(0, 6);
//            psgFlightNoList.add(flightNo);
//        }
//        // 获取出发地为上海且出发时间在8：00-22:00的航班
//        List<FlightGroupDto> shaFlightList = stsJobMapper.retrieveShaFlight();
//        shaFlightList.forEach(o -> o.setHavePsg(psgFlightNoList.contains(o.getFlightNo()) ? "1" : "0"));
//        // 登机口列表
//        List<List<FlightGroupDto>> gateList = new ArrayList<>();
//        // 登机口内的航班列表
//        List<FlightGroupDto> gateFlightList = new ArrayList<>();
//        // 分配登机口
//        shaFlightList.get(0).setGate("1");
//        gateFlightList.add(shaFlightList.get(0));
//        gateList.add(gateFlightList);
//        for (int i = 1; i < shaFlightList.size(); i++) {
//            DateTime time1 = DateUtil.parse(shaFlightList.get(i).getDepTime(), "HHss");
//            int g = 0;
//            // 遍历登机口
//            for (List<FlightGroupDto> gateFltList : gateList) {
//                int lastIndex = gateFltList.size() - 1;
//                //获取到该登机口最后一个航班的起飞时间
//                DateTime time2 = DateUtil.parse(gateFltList.get(lastIndex).getDepTime(), "HHss");
//                //相差分钟数
//                long minute = DateUtil.between(time1, time2, DateUnit.MINUTE);
//                if (30 <= minute) {
//                    shaFlightList.get(i).setGate(String.valueOf(g + 1));
//                    gateFltList.add(shaFlightList.get(i));
//                    break;
//                } else if (g == gateList.size() - 1) {
//                    //新建一个登机口
//                    List<FlightGroupDto> gate = new ArrayList<>();
//                    shaFlightList.get(i).setGate(String.valueOf(g + 2));
//                    gate.add(shaFlightList.get(i));
//                    gateList.add(gate);
//                    break;
//                }
//                g++;
//            }
//        }
//        // 获取出发城市不为上海的航班 并随机分配1-14号登机口
//        List<MnjxFlightScreen> noShaFlightList = stsJobMapper.retrieveNoShaFlight();
//        for (MnjxFlightScreen flightScreen : noShaFlightList) {
//            Random rd = new Random();
//            int num = rd.nextInt(13) + 1;
//            flightScreen.setGate(String.valueOf(num));
//            flightScreen.setScreenStatus("OP");
//        }
//        List<MnjxFlightScreen> flightScreens = new ArrayList<>();
//        for (List<FlightGroupDto> flightGroupDtos : gateList) {
//            // 一个登机口内取消或延误航班的数量
//            int num = 0;
//            // 分配航班状态
//            for (FlightGroupDto flight : flightGroupDtos) {
//                boolean isWorkTime = Integer.parseInt(flight.getDepTime()) > 800 && Integer.parseInt(flight.getDepTime()) < 2200;
//                // 只有前三个航班需要设置延误和取消
//                if ("0".equals(flight.getHavePsg()) && isWorkTime && num == 0) {
//                    flight.setScreenStatus("DELAY");
//                    num++;
//                } else if ("0".equals(flight.getHavePsg()) && isWorkTime && num == 1) {
//                    flight.setScreenStatus("XX");
//                    num++;
//                } else {
//                    flight.setScreenStatus("OP");
//                }
//            }
//            List<MnjxFlightScreen> mnjxFlightScreens = BeanUtil.copyToList(flightGroupDtos, MnjxFlightScreen.class);
//            flightScreens.addAll(mnjxFlightScreens);
//        }
//        flightScreens.addAll(noShaFlightList);
//        iMnjxFlightScreenService.remove(null);
//        iMnjxFlightScreenService.saveOrUpdateBatch(flightScreens);
//    }

    /**
     * 原始代码，查询出所有要删除的数据，进行分批次删除
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void deleteHistoryDate() {
        List<String> pnrIds = new ArrayList<>();
        List<MnjxPnrSeg> pnrSegDeletes = new ArrayList<>();
        Date nowDate = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(nowDate);
        cal.add(Calendar.DATE, -Constant.SEVEN);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String selectDate = simpleDateFormat.format(cal.getTime());
        List<MnjxPnrSeg> mnjxPnrSegs = iMnjxPnrSegService.lambdaQuery().lt(MnjxPnrSeg::getFlightDate, selectDate).list();
        mnjxPnrSegs.forEach(mnjxPnrSeg -> {
            if (!pnrIds.contains(mnjxPnrSeg.getPnrId())) {
                List<MnjxPnrSeg> mnjxPnrSegSamePnr = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, mnjxPnrSeg.getPnrId()).list();
                if (ObjectUtil.isNotEmpty(mnjxPnrSegSamePnr)) {
                    if (mnjxPnrSegSamePnr.size() != Constant.ONE) {
                        //验证所有航段起飞时间都到期
                        List<MnjxPnrSeg> mnjxPnrSegCanDelPnr = mnjxPnrSegSamePnr.stream().filter(s -> !Constant.SA.equals(s.getPnrSegType())).filter(s -> {
                            try {
                                return simpleDateFormat.parse(s.getFlightDate()).getTime() < cal.getTime().getTime();
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            return false;
                        }).collect(Collectors.toList());
                        long saCount = mnjxPnrSegSamePnr.stream()
                                .filter(s -> Constant.SA.equals(s.getPnrSegType()))
                                .count();
                        if (mnjxPnrSegCanDelPnr.size() + saCount == mnjxPnrSegSamePnr.size()) {
                            pnrIds.add(mnjxPnrSeg.getPnrId());
                            pnrSegDeletes.add(mnjxPnrSeg);
                        }
                    } else {
                        pnrIds.add(mnjxPnrSeg.getPnrId());
                        pnrSegDeletes.add(mnjxPnrSeg);
                    }
                }
            }
        });
        //删除航班信息。
        this.deleteSeat(pnrSegDeletes, selectDate);
        //删除pnr
        if (ObjectUtil.isNotEmpty(pnrIds)) {
            log.info("开始清理PNR相关数据");
            List<MnjxPnrTk> existTks = iMnjxPnrTkService.lambdaQuery().in(MnjxPnrTk::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrTkService, existTks.stream().map(MnjxPnrTk::getPnrTkId).collect(Collectors.toList()));

            List<MnjxPnrSeg> existSegs = iMnjxPnrSegService.lambdaQuery().in(MnjxPnrSeg::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrSegService, existSegs.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList()));

            List<MnjxPnrOsi> existOsis = iMnjxPnrOsiService.lambdaQuery().in(MnjxPnrOsi::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrOsiService, existOsis.stream().map(MnjxPnrOsi::getPnrOsiId).collect(Collectors.toList()));

            List<MnjxPnrGn> existGns = iMnjxPnrGnService.lambdaQuery().in(MnjxPnrGn::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrGnService, existGns.stream().map(MnjxPnrGn::getPnrGnId).collect(Collectors.toList()));

            List<MnjxPnrFc> existFcs = iMnjxPnrFcService.lambdaQuery().in(MnjxPnrFc::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrFcService, existFcs.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));

            List<MnjxPnrEi> existEis = iMnjxPnrEiService.lambdaQuery().in(MnjxPnrEi::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrEiService, existEis.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));

            List<MnjxPnrTc> existTcs = iMnjxPnrTcService.lambdaQuery().in(MnjxPnrTc::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrTcService, existTcs.stream().map(MnjxPnrTc::getPnrTcId).collect(Collectors.toList()));

            List<MnjxPnrAt> existAts = iMnjxPnrAtService.lambdaQuery().in(MnjxPnrAt::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrAtService, existAts.stream().map(MnjxPnrAt::getPnrAtId).collect(Collectors.toList()));

            List<MnjxPnrFn> existFns = iMnjxPnrFnService.lambdaQuery().in(MnjxPnrFn::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrFnService, existFns.stream().map(MnjxPnrFn::getPnrFnId).collect(Collectors.toList()));

            List<MnjxPnrFp> existFps = iMnjxPnrFpService.lambdaQuery().in(MnjxPnrFp::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrFpService, existFps.stream().map(MnjxPnrFp::getPnrFpId).collect(Collectors.toList()));

            List<MnjxPnrRecord> existRecords = iMnjxPnrRecordService.lambdaQuery().in(MnjxPnrRecord::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrRecordService, existRecords.stream().map(MnjxPnrRecord::getPnrRecordId).collect(Collectors.toList()));

            List<MnjxPnrRmk> existRmks = iMnjxPnrRmkService.lambdaQuery().in(MnjxPnrRmk::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrRmkService, existRmks.stream().map(MnjxPnrRmk::getPnrRmkId).collect(Collectors.toList()));

            List<MnjxPnrCt> existCts = iMnjxPnrCtService.lambdaQuery().in(MnjxPnrCt::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxPnrCtService, existCts.stream().map(MnjxPnrCt::getPnrCtId).collect(Collectors.toList()));

            List<MnjxMent> existMents = iMnjxMentService.lambdaQuery().in(MnjxMent::getPnrId, pnrIds).list();
            this.deleteDate(iMnjxMentService, existMents.stream().map(MnjxMent::getIdBgt).collect(Collectors.toList()));

            List<MnjxPnrNm> existNms = iMnjxPnrNmService.lambdaQuery().in(MnjxPnrNm::getPnrId, pnrIds).list();
            //删除nm
            log.info("开始清理PNR旅客相关数据");
            this.deleteNmDate(existNms);
            this.deleteDate(iMnjxPnrService, pnrIds);
        }
    }

    /**
     * 删除seat
     *
     * @param pnrSegDeletes 航段数据
     * @param selectDate    时间筛选条件
     */
    private void deleteSeat(List<MnjxPnrSeg> pnrSegDeletes, String selectDate) {
        List<PlanFlightNoDateVo> planFlightNoDateVos = iPlanFlightService.retrieveNoDate(selectDate);
        List<String> planFlightIds = new ArrayList<>();
        pnrSegDeletes.forEach(mnjxPnrSeg -> {
                    if (CollUtil.isNotEmpty(planFlightNoDateVos)) {
                        planFlightNoDateVos.forEach(planFlightNoDateVo -> {
                            if (mnjxPnrSeg.getFlightDate().equals(planFlightNoDateVo.getFlightDate()) && mnjxPnrSeg.getFlightNo().equals(planFlightNoDateVo.getFlightNo())) {
                                planFlightIds.add(planFlightNoDateVo.getPlanFlightId());
                            }
                        });
                    }
                }

        );
        if (ObjectUtil.isNotEmpty(planFlightIds)) {
            List<MnjxPlanSection> existPlanSections = iMnjxPlanSectionService.lambdaQuery().in(MnjxPlanSection::getPlanFlightId, planFlightIds).list();
            if (ObjectUtil.isNotEmpty(existPlanSections)) {
                List<String> planSectionIds = existPlanSections.stream().map(MnjxPlanSection::getPlanSectionId).collect(Collectors.toList());
                List<MnjxLuggageUnmanned> existLuggageUnmanneds = iMnjxLuggageUnmannedService.lambdaQuery().in(MnjxLuggageUnmanned::getPlanSectionId, planSectionIds).list();
                log.info("开始清理无人行李表数据");
                this.deleteDate(iMnjxLuggageUnmannedService, existLuggageUnmanneds.stream().map(MnjxLuggageUnmanned::getLuggageId).collect(Collectors.toList()));
                List<MnjxOpenCabin> existOpenCabins = iMnjxOpenCabinService.lambdaQuery().in(MnjxOpenCabin::getPlanSectionId, planSectionIds).list();
                if (ObjectUtil.isNotEmpty(existOpenCabins)) {
                    List<String> openCabinIds = existOpenCabins.stream().map(MnjxOpenCabin::getOpenCabinId).collect(Collectors.toList());
                    List<MnjxSeat> existSeats = iMnjxSeatService.lambdaQuery().in(MnjxSeat::getOpenCabinId, openCabinIds).list();
                    log.info("开始清理座位表数据");
                    this.deleteDate(iMnjxSeatService, existSeats.stream().map(MnjxSeat::getSeatId).collect(Collectors.toList()));
                    log.info("开始清理开舱表数据");
                    this.deleteDate(iMnjxOpenCabinService, openCabinIds);
                }
                log.info("开始清理计划航节表数据");
                this.deleteDate(iMnjxPlanSectionService, planSectionIds);
            }
            log.info("开始清理计划航班表数据");
            this.deleteDate(iMnjxPlanFlightService, planFlightIds);
        }
    }


    /**
     * 删除pnr相关nm
     *
     * @param existNms nm
     */
    private void deleteNmDate(List<MnjxPnrNm> existNms) {
        if (ObjectUtil.isNotEmpty(existNms)) {
            List<String> nmIds = existNms.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
            List<MnjxNmFc> existNmFcs = iMnjxNmFcService.lambdaQuery().in(MnjxNmFc::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmFcService, existNmFcs.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
            List<MnjxNmEi> existNmEis = iMnjxNmEiService.lambdaQuery().in(MnjxNmEi::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmEiService, existNmEis.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
            List<MnjxNmCt> existNmCts = iMnjxNmCtService.lambdaQuery().in(MnjxNmCt::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmCtService, existNmCts.stream().map(MnjxNmCt::getPnrCtId).collect(Collectors.toList()));
            List<MnjxPnrNmUm> existPnrNmUms = iMnjxPnrNmUmService.lambdaQuery().in(MnjxPnrNmUm::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxPnrNmUmService, existPnrNmUms.stream().map(MnjxPnrNmUm::getNmUmId).collect(Collectors.toList()));
            List<MnjxNmFn> existNmFns = iMnjxNmFnService.lambdaQuery().in(MnjxNmFn::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmFnService, existNmFns.stream().map(MnjxNmFn::getNmFnId).collect(Collectors.toList()));
            List<MnjxNmXn> existNmXns = iMnjxNmXnService.lambdaQuery().in(MnjxNmXn::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmXnService, existNmXns.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()));
            List<MnjxNmOi> existNmOis = iMnjxNmOiService.lambdaQuery().in(MnjxNmOi::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmOiService, existNmOis.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList()));
            List<MnjxNmOsi> existNmOsis = iMnjxNmOsiService.lambdaQuery().in(MnjxNmOsi::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmOsiService, existNmOsis.stream().map(MnjxNmOsi::getPnrOsiId).collect(Collectors.toList()));
            List<MnjxNmRmk> existNmRmks = iMnjxNmRmkService.lambdaQuery().in(MnjxNmRmk::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmRmkService, existNmRmks.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
            List<MnjxNmSsr> existNmSsrs = iMnjxNmSsrService.lambdaQuery().in(MnjxNmSsr::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmSsrService, existNmSsrs.stream().map(MnjxNmSsr::getNmSsrId).collect(Collectors.toList()));
            List<MnjxNmFp> existNmFps = iMnjxNmFpService.lambdaQuery().in(MnjxNmFp::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxNmFpService, existNmFps.stream().map(MnjxNmFp::getNmFpId).collect(Collectors.toList()));
            List<MnjxLuggageCarryon> existLuggageCarryons = iMnjxLuggageCarryonService.lambdaQuery().in(MnjxLuggageCarryon::getPnrNmId, nmIds).list();
            this.deleteDate(iMnjxLuggageCarryonService, existLuggageCarryons.stream().map(MnjxLuggageCarryon::getLuggageCarryonId).collect(Collectors.toList()));
            List<MnjxLuggage> existLuggages = iMnjxLuggageService.lambdaQuery().in(MnjxLuggage::getPnrNmId, nmIds).list();

            List<MnjxUnpackInfo> unpackInfoList = iMnjxUnpackInfoService.lambdaQuery().in(CollUtil.isNotEmpty(existLuggages), MnjxUnpackInfo::getLuggageId, existLuggages.stream().map(MnjxLuggage::getLuggageId).collect(Collectors.toList())).list();
            if (CollUtil.isNotEmpty(unpackInfoList)) {
                List<String> unpackInfoIdList = unpackInfoList.stream().map(MnjxUnpackInfo::getUnpackId).collect(Collectors.toList());
                this.deleteDate(iMnjxUnpackInfoService, unpackInfoIdList);
            }

            this.deleteDate(iMnjxLuggageService, existLuggages.stream().map(MnjxLuggage::getLuggageId).collect(Collectors.toList()));

            List<MnjxPnrNmTn> existPnrNmTns = iMnjxPnrNmTnService.lambdaQuery().in(MnjxPnrNmTn::getPnrNmId, nmIds).list();
            if (ObjectUtil.isNotEmpty(existPnrNmTns)) {
                List<String> tnIds = existPnrNmTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                List<MnjxPnrNmTicket> existPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getPnrNmTnId, nmIds).list();
                this.deleteDate(iMnjxPnrNmTicketService, existPnrNmTickets.stream().map(MnjxPnrNmTicket::getNmTicketId).collect(Collectors.toList()));

                List<String> ticketNoList = existPnrNmTickets.stream().map(MnjxPnrNmTicket::getTicketNo).collect(Collectors.toList());
                List<MnjxTicketOperateRecord> ticketOperateRecordList = iMnjxTicketOperateRecordService.lambdaQuery().in(CollUtil.isNotEmpty(ticketNoList), MnjxTicketOperateRecord::getTicketNo, ticketNoList).list();
                if (CollUtil.isNotEmpty(ticketOperateRecordList)) {
                    List<String> ticketOperateRecordIdList = ticketOperateRecordList.stream().map(MnjxTicketOperateRecord::getTicketOperateRecordId).collect(Collectors.toList());
                    this.deleteDate(iMnjxTicketOperateRecordService, ticketOperateRecordIdList);
                }

                List<MnjxRefundTicket> refundTicketList = iMnjxRefundTicketService.lambdaQuery().in(CollUtil.isNotEmpty(ticketNoList), MnjxRefundTicket::getTicketNo, ticketNoList).list();
                if (CollUtil.isNotEmpty(refundTicketList)) {
                    List<String> refundTicketIdList = refundTicketList.stream().map(MnjxRefundTicket::getRefundId).collect(Collectors.toList());
                    this.deleteDate(iMnjxRefundTicketService, refundTicketIdList);
                }

                this.deleteDate(iMnjxPnrNmTnService, tnIds);
            }
            List<MnjxPsgCki> existPsgCkis = iMnjxPsgCkiService.lambdaQuery().in(MnjxPsgCki::getPnrNmId, nmIds).list();
            if (ObjectUtil.isNotEmpty(existPsgCkis)) {
                List<String> psgCkiIds = existPsgCkis.stream().map(MnjxPsgCki::getPsgCkiId).collect(Collectors.toList());
                List<MnjxExLuggage> existExLuggages = iMnjxExLuggageService.lambdaQuery().in(MnjxExLuggage::getPsgCkiId, psgCkiIds).list();
                this.deleteDate(iMnjxExLuggageService, existExLuggages.stream().map(MnjxExLuggage::getExLuggageId).collect(Collectors.toList()));
                List<MnjxPsgCkiOption> existPsgCkiOptions = iMnjxPsgCkiOptionService.lambdaQuery().in(MnjxPsgCkiOption::getPsgCkiId, psgCkiIds).list();
                this.deleteDate(iMnjxPsgCkiOptionService, existPsgCkiOptions.stream().map(MnjxPsgCkiOption::getPsgCkiOptionId).collect(Collectors.toList()));
                List<MnjxPsgSeat> existPsgSeats = iMnjxPsgSeatService.lambdaQuery().in(MnjxPsgSeat::getPsgCkiId, psgCkiIds).list();
                this.deleteDate(iMnjxPsgSeatService, existPsgSeats.stream().map(MnjxPsgSeat::getPsgSeatId).collect(Collectors.toList()));
                List<MnjxPsgOperateRecord> existPsgOperateRecords = iMnjxPsgOperateRecordService.lambdaQuery().in(MnjxPsgOperateRecord::getPsgCkiId, psgCkiIds).list();
                this.deleteDate(iMnjxPsgOperateRecordService, existPsgOperateRecords.stream().map(MnjxPsgOperateRecord::getOperateRecordId).collect(Collectors.toList()));
                this.deleteDate(iMnjxPsgCkiService, psgCkiIds);

                List<MnjxVerifyInfo> verifyInfoList = iMnjxVerifyInfoService.lambdaQuery().in(MnjxVerifyInfo::getPsgCkiId, psgCkiIds).list();
                if (CollUtil.isNotEmpty(verifyInfoList)) {
                    List<String> verifyInfoIdList = verifyInfoList.stream().map(MnjxVerifyInfo::getVerifyId).collect(Collectors.toList());
                    this.deleteDate(iMnjxVerifyInfoService, verifyInfoIdList);
                }
            }
            this.deleteDate(iMnjxPnrNmService, nmIds);
        }
    }

    /**
     * 分批删除
     *
     * @param iService service
     * @param exists   idList
     */
    private void deleteDate(IService<?> iService, List<String> exists) {
        if (CollUtil.isEmpty(exists)) {
            return;
        }
        if (exists.size() <= Constant.ONE_SECOND_1000) {
            iService.removeByIds(exists);
        } else {
            int a = Constant.ZERO;
            while (true) {
                int b = a + Constant.ONE_SECOND_1000;
                if (b < exists.size()) {
                    log.debug("当前线程名称开始删除：" + Thread.currentThread().getId());
                    iService.removeByIds(exists.subList(a, b));
                    log.debug("当前线程名称结束删除：" + Thread.currentThread().getId());
                } else {
                    log.debug("当前线程名称开始删除：" + Thread.currentThread().getId());
                    iService.removeByIds(exists.subList(a, exists.size()));
                    log.debug("当前线程名称结束删除：" + Thread.currentThread().getId());
                    break;
                }
                a = a + Constant.ONE_SECOND_1000;
            }
        }
    }

    //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

    /**
     * 先查询出需要删除的所有日期，然后，7天为一组，进行查询，进行删除，执行完在进行下一组
     */
    @Override
    public void deleteHistoryDateGroup() {
        String selectDate = DateUtils.date2ymd(DateUtils.offsetDay(DateUtil.date(), -Constant.SEVEN));
        log.info("即将清理 {} 之前的数据", selectDate);
        //查询需要删除的所有日期
        List<String> delSegDates = stsJobMapper.retrieveSegDate(selectDate);
        log.info("总共需要清理{}天的数据", delSegDates.size());
        log.info("总共需要清理的日期：{}", delSegDates.toString());
        //把日期按照7天为一组进行清除数据
        int chunkSize = 7;
        List<List<String>> segDateGroup = IntStream.range(0, (delSegDates.size() + chunkSize - 1) / chunkSize)
                .mapToObj(i -> delSegDates.subList(i * chunkSize, Math.min((i + 1) * chunkSize, delSegDates.size())))
                .collect(toList());
        for (List<String> dateList : segDateGroup) {
            log.info("查询操作-日期：{}", dateList);
            FltAndPsgVo fltAndPsgVo = new FltAndPsgVo();
            SeatVo seatVo = fltAndPsgVo.getSeatVo();
            PnrInfoVo pnrInfoVo = fltAndPsgVo.getPnrInfoVo();
            NmInfoVo nmInfoVo = fltAndPsgVo.getNmInfoVo();
            //7天的数据
            List<MnjxPnrSeg> mnjxPnrSegs = this.retrievePnrSeg(dateList);
            //根据pnrId分组
            Map<String, List<MnjxPnrSeg>> pnrGroups = mnjxPnrSegs.stream().collect(Collectors.groupingBy(MnjxPnrSeg::getPnrId));

            List<String> pnrIds = new ArrayList<>();
            List<MnjxPnrSeg> pnrSegDeletes = new ArrayList<>();
            //处理SA航段数据
            for (Map.Entry<String, List<MnjxPnrSeg>> entry : pnrGroups.entrySet()) {
                String pnrId = entry.getKey();
                List<MnjxPnrSeg> mnjxPnrSegSamePnr = entry.getValue();
                if (mnjxPnrSegSamePnr.size() != Constant.ONE) {
                    //验证所有航段起飞时间都到期
                    List<MnjxPnrSeg> mnjxPnrSegCanDelPnr = mnjxPnrSegSamePnr.stream().filter(s -> !Constant.SA.equals(s.getPnrSegType())).filter(s -> DateUtils.ymd2Date(s.getFlightDate()).compareTo(DateUtils.ymd2Date(selectDate)) < 0).collect(Collectors.toList());
                    long saCount = mnjxPnrSegSamePnr.stream().filter(s -> Constant.SA.equals(s.getPnrSegType())).count();
                    if (mnjxPnrSegCanDelPnr.size() + saCount == mnjxPnrSegSamePnr.size()) {
                        pnrIds.add(pnrId);
                        pnrSegDeletes.addAll(mnjxPnrSegSamePnr);
                    }
                } else {
                    pnrIds.add(pnrId);
                    pnrSegDeletes.addAll(mnjxPnrSegSamePnr);
                }
            }
            pnrSegDeletes.removeAll(Collections.singleton(null));
            //查询需要删除的数据
            this.selectData(pnrIds, pnrSegDeletes, dateList, seatVo, pnrInfoVo, nmInfoVo);
            log.info("开始删除操作-日期：{}", dateList);
            //开始删除操作
            iTransactionalService.clearData(seatVo, pnrInfoVo, nmInfoVo);
        }
    }

    public void deletePrintData() {
        iMnjxPrintDataService.lambdaUpdate()
                .lt(MnjxPrintData::getCreateTime, DateUtil.yesterday())
                .remove();
    }

    @Override
    public void recordJobExecute(String jobName) {
        MnjxJobExecute jobExecute = new MnjxJobExecute();
        jobExecute.setJobName(jobName);
        jobExecute.setExecuteDate(DateUtil.today());
        iMnjxJobExecuteService.save(jobExecute);
    }

    @Override
    public boolean isExecute(String jobName) {
        Date now = new Date();
        String today = DateUtil.today();
        // 旅客生成任务判断时间周期为上周六-本周五（包含）
        if ("psgJobHandler".equals(jobName)) {
            int week = DateUtil.dayOfWeek(now);
            // 周六时直接调用
            if (week == 7) {
                return false;
            }
            // 非周六时，往回推到上个周六的日期，查询是否已执行过
            String saturdayYmd = "";
            while (StrUtil.isEmpty(saturdayYmd)) {
                now = DateUtil.offset(now, DateField.DAY_OF_WEEK, -1);
                week = DateUtil.dayOfWeek(now);
                if (week == 7) {
                    saturdayYmd = DateUtils.date2ymd(now);
                }
            }
            List<MnjxJobExecute> executeList = iMnjxJobExecuteService.lambdaQuery()
                    .eq(MnjxJobExecute::getJobName, jobName)
                    .between(MnjxJobExecute::getExecuteDate, saturdayYmd, today)
                    .list();
            return CollUtil.isNotEmpty(executeList);
        }
        // 航班生成任务判断时间周期为本月1号-当前
        else {
            int month = DateUtil.dayOfMonth(now);
            // 每月1号时直接调用
            if (month == 1) {
                return false;
            }
            // 非1号时查询本月1号到当天是否已执行过
            String firstDayOfThisMonth = StrUtil.format("{}-01", today.substring(0, 7));
            List<MnjxJobExecute> executeList = iMnjxJobExecuteService.lambdaQuery()
                    .eq(MnjxJobExecute::getJobName, jobName)
                    .between(MnjxJobExecute::getExecuteDate, firstDayOfThisMonth, today)
                    .list();
            return CollUtil.isNotEmpty(executeList);
        }
    }

    private List<MnjxPnrSeg> retrievePnrSeg(List<String> dateList) {
        //根据不同日期，查询每天订座的航班数据
        List<MnjxPnrSeg> mnjxPnrSegs = iMnjxPnrSegService.lambdaQuery().in(MnjxPnrSeg::getFlightDate, dateList).list();
        List<String> segPnrIds = mnjxPnrSegs.stream().map(MnjxPnrSeg::getPnrId).collect(Collectors.toList());
        //查询日期为空的，添加SA航段的数据
        List<MnjxPnrSeg> pnrSegs = iMnjxPnrSegService.lambdaQuery().isNull(MnjxPnrSeg::getFlightDate).list();
        if (CollUtil.isNotEmpty(pnrSegs)) {
            List<MnjxPnrSeg> segs = pnrSegs.stream().filter(segId -> segPnrIds.contains(segId.getPnrSegId())).collect(Collectors.toList());
            mnjxPnrSegs.addAll(segs);
        }
        return mnjxPnrSegs;
    }

    private void selectData(List<String> pnrIds, List<MnjxPnrSeg> pnrSegDeletes, List<String> dateList, SeatVo seatVo, PnrInfoVo pnrInfoVo, NmInfoVo nmInfoVo) {
        //查询座位
        log.info("查询要清理的座位信息");
        this.selectSeat(pnrSegDeletes, dateList, seatVo);
        //查询pnr相关信息
        log.info("查询要清理的PNR信息");
        this.selectPnrInfo(pnrIds, pnrInfoVo, nmInfoVo);
    }

    private void selectSeat(List<MnjxPnrSeg> pnrSegDeletes, List<String> dateList, SeatVo seatVo) {
        List<PlanFlightNoDateVo> planFlightNoDateVos = iPlanFlightService.retrieveNoDateList(dateList);
        List<String> planFlightIds = new ArrayList<>();
        pnrSegDeletes.forEach(mnjxPnrSeg -> {
                    if (CollUtil.isNotEmpty(planFlightNoDateVos)) {
                        planFlightNoDateVos.forEach(planFlightNoDateVo -> {
                            if (mnjxPnrSeg.getFlightDate().equals(planFlightNoDateVo.getFlightDate()) && mnjxPnrSeg.getFlightNo().equals(planFlightNoDateVo.getFlightNo())) {
                                planFlightIds.add(planFlightNoDateVo.getPlanFlightId());
                            }
                        });
                    }
                }
        );
        if (ObjectUtil.isNotEmpty(planFlightIds)) {
            List<MnjxPlanSection> existPlanSections = iMnjxPlanSectionService.lambdaQuery().in(MnjxPlanSection::getPlanFlightId, planFlightIds).list();
            if (ObjectUtil.isNotEmpty(existPlanSections)) {
                List<String> planSectionIds = existPlanSections.stream().map(MnjxPlanSection::getPlanSectionId).collect(Collectors.toList());
                List<MnjxLuggageUnmanned> existLuggageUnmanneds = iMnjxLuggageUnmannedService.lambdaQuery().in(MnjxLuggageUnmanned::getPlanSectionId, planSectionIds).list();

                List<String> luggageIds = existLuggageUnmanneds.stream().map(MnjxLuggageUnmanned::getLuggageId).collect(Collectors.toList());
                seatVo.getLuggageIdList().addAll(luggageIds);

                List<MnjxOpenCabin> existOpenCabins = iMnjxOpenCabinService.lambdaQuery().in(MnjxOpenCabin::getPlanSectionId, planSectionIds).list();
                if (ObjectUtil.isNotEmpty(existOpenCabins)) {
                    List<String> openCabinIds = existOpenCabins.stream().map(MnjxOpenCabin::getOpenCabinId).collect(Collectors.toList());
                    List<MnjxSeat> existSeats = iMnjxSeatService.lambdaQuery().in(MnjxSeat::getOpenCabinId, openCabinIds).list();

                    List<String> seatIds = existSeats.stream().map(MnjxSeat::getSeatId).collect(Collectors.toList());
                    seatVo.getSeatIdList().addAll(seatIds);
                    seatVo.getOpenCabinList().addAll(openCabinIds);
                }
                seatVo.getPlanSectionList().addAll(planSectionIds);
            }
            seatVo.getPlanFlightIdList().addAll(planFlightIds);
        }
    }

    private void selectPnrInfo(List<String> pnrIds, PnrInfoVo pnrInfoVo, NmInfoVo nmInfoVo) {
        if (ObjectUtil.isNotEmpty(pnrIds)) {
            log.info("查询PNR TK");
            List<MnjxPnrTk> existTks = iMnjxPnrTkService.lambdaQuery().in(MnjxPnrTk::getPnrId, pnrIds).list();
            List<String> tkIds = existTks.stream().map(MnjxPnrTk::getPnrTkId).collect(Collectors.toList());
            pnrInfoVo.getTkList().addAll(tkIds);

            log.info("查询PNR SEG");
            List<MnjxPnrSeg> existSegs = iMnjxPnrSegService.lambdaQuery().in(MnjxPnrSeg::getPnrId, pnrIds).list();
            List<String> pnrSegIds = existSegs.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
            pnrInfoVo.getPnrSegIdList().addAll(pnrSegIds);

            log.info("查询PNR OSI");
            List<MnjxPnrOsi> existOsis = iMnjxPnrOsiService.lambdaQuery().in(MnjxPnrOsi::getPnrId, pnrIds).list();
            List<String> pnrOsiIds = existOsis.stream().map(MnjxPnrOsi::getPnrOsiId).collect(Collectors.toList());
            pnrInfoVo.getPnrOsiIdList().addAll(pnrOsiIds);

            log.info("查询PNR GN");
            List<MnjxPnrGn> existGns = iMnjxPnrGnService.lambdaQuery().in(MnjxPnrGn::getPnrId, pnrIds).list();
            List<String> pnrGnIds = existGns.stream().map(MnjxPnrGn::getPnrGnId).collect(Collectors.toList());
            pnrInfoVo.getPnrGnIdList().addAll(pnrGnIds);

            log.info("查询PNR FC");
            List<MnjxPnrFc> existFcs = iMnjxPnrFcService.lambdaQuery().in(MnjxPnrFc::getPnrId, pnrIds).list();
            List<String> pnrFcIds = existFcs.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList());
            pnrInfoVo.getPnrFcIdList().addAll(pnrFcIds);

            log.info("查询PNR EI");
            List<MnjxPnrEi> existEis = iMnjxPnrEiService.lambdaQuery().in(MnjxPnrEi::getPnrId, pnrIds).list();
            List<String> pnrEiIds = existEis.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList());
            pnrInfoVo.getPnrEiIdList().addAll(pnrEiIds);

            log.info("查询PNR TC");
            List<MnjxPnrTc> existTcs = iMnjxPnrTcService.lambdaQuery().in(MnjxPnrTc::getPnrId, pnrIds).list();
            List<String> pnrTcIds = existTcs.stream().map(MnjxPnrTc::getPnrTcId).collect(Collectors.toList());
            pnrInfoVo.getPnrTcIdList().addAll(pnrTcIds);

            log.info("查询PNR AT");
            List<MnjxPnrAt> existAts = iMnjxPnrAtService.lambdaQuery().in(MnjxPnrAt::getPnrId, pnrIds).list();
            List<String> pnrAtIds = existAts.stream().map(MnjxPnrAt::getPnrAtId).collect(Collectors.toList());
            pnrInfoVo.getPnrAtList().addAll(pnrAtIds);

            log.info("查询PNR FN");
            List<MnjxPnrFn> existFns = iMnjxPnrFnService.lambdaQuery().in(MnjxPnrFn::getPnrId, pnrIds).list();
            List<String> pnrFnIds = existFns.stream().map(MnjxPnrFn::getPnrFnId).collect(Collectors.toList());
            pnrInfoVo.getPnrFnIdList().addAll(pnrFnIds);

            log.info("查询PNR FP");
            List<MnjxPnrFp> existFps = iMnjxPnrFpService.lambdaQuery().in(MnjxPnrFp::getPnrId, pnrIds).list();
            List<String> pnrFpIds = existFps.stream().map(MnjxPnrFp::getPnrFpId).collect(Collectors.toList());
            pnrInfoVo.getPnrFpIdList().addAll(pnrFpIds);

            //进行分批查询
            log.info("查询PNR RECORD");
            List<MnjxPnrRecord> existRecords = iMnjxPnrRecordService.lambdaQuery().in(MnjxPnrRecord::getPnrId, pnrIds).list();
            List<String> pnrRecordIds = existRecords.stream().map(MnjxPnrRecord::getPnrRecordId).collect(Collectors.toList());
            pnrInfoVo.getPnrRecordIdList().addAll(pnrRecordIds);

            log.info("查询PNR RMK");
            List<MnjxPnrRmk> existRmks = iMnjxPnrRmkService.lambdaQuery().in(MnjxPnrRmk::getPnrId, pnrIds).list();
            List<String> pnrRmkIds = existRmks.stream().map(MnjxPnrRmk::getPnrRmkId).collect(Collectors.toList());
            pnrInfoVo.getPnrRmkIdList().addAll(pnrRmkIds);

            log.info("查询PNR CT");
            List<MnjxPnrCt> existCts = iMnjxPnrCtService.lambdaQuery().in(MnjxPnrCt::getPnrId, pnrIds).list();
            List<String> pnrCtIds = existCts.stream().map(MnjxPnrCt::getPnrCtId).collect(Collectors.toList());
            pnrInfoVo.getPnrCtIdList().addAll(pnrCtIds);

            log.info("查询关注的PNR");
            List<MnjxPnrSub> pnrSubList = iMnjxPnrSubService.lambdaQuery().in(MnjxPnrSub::getPnrId, pnrIds).list();
            List<String> pnrSubIdList = pnrSubList.stream().map(MnjxPnrSub::getMnjxPnrSubId).collect(toList());
            pnrInfoVo.getPnrSubIdList().addAll(pnrSubIdList);

            log.info("查询MENT");
            List<MnjxMent> existMents = iMnjxMentService.lambdaQuery().in(MnjxMent::getPnrId, pnrIds).list();
            List<String> idBgts = existMents.stream().map(MnjxMent::getIdBgt).collect(Collectors.toList());
            pnrInfoVo.getIdBgtList().addAll(idBgts);

            log.info("查询PNR NM");
            List<MnjxPnrNm> existNms = iMnjxPnrNmService.lambdaQuery().in(MnjxPnrNm::getPnrId, pnrIds).list();

            this.selectNmInfo(existNms, nmInfoVo);
            pnrInfoVo.getPnrIdsList().addAll(pnrIds);
        }
    }

    private void selectNmInfo(List<MnjxPnrNm> existNms, NmInfoVo nmInfoVo) {
        if (ObjectUtil.isNotEmpty(existNms)) {
            List<String> nmIds = existNms.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());

            log.info("查询NM FC");
            List<MnjxNmFc> existNmFcs = iMnjxNmFcService.lambdaQuery().in(MnjxNmFc::getPnrNmId, nmIds).list();
            List<String> nmFcIds = existNmFcs.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList());
            nmInfoVo.getNmFcIdList().addAll(nmFcIds);

            log.info("查询NM EI");
            List<MnjxNmEi> existNmEis = iMnjxNmEiService.lambdaQuery().in(MnjxNmEi::getPnrNmId, nmIds).list();
            List<String> nmEiIds = existNmEis.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList());
            nmInfoVo.getNmEiIdLIst().addAll(nmEiIds);

            log.info("查询NM CT");
            List<MnjxNmCt> existNmCts = iMnjxNmCtService.lambdaQuery().in(MnjxNmCt::getPnrNmId, nmIds).list();
            List<String> nmCtIds = existNmCts.stream().map(MnjxNmCt::getPnrCtId).collect(Collectors.toList());
            nmInfoVo.getNmCtIdList().addAll(nmCtIds);

            log.info("查询NM UM");
            List<MnjxPnrNmUm> existPnrNmUms = iMnjxPnrNmUmService.lambdaQuery().in(MnjxPnrNmUm::getPnrNmId, nmIds).list();
            List<String> nmUmIds = existPnrNmUms.stream().map(MnjxPnrNmUm::getNmUmId).collect(Collectors.toList());
            nmInfoVo.getNmUmIdList().addAll(nmUmIds);

            log.info("查询NM FN");
            List<MnjxNmFn> existNmFns = iMnjxNmFnService.lambdaQuery().in(MnjxNmFn::getPnrNmId, nmIds).list();
            List<String> nmFnIds = existNmFns.stream().map(MnjxNmFn::getNmFnId).collect(Collectors.toList());
            nmInfoVo.getNmFnIdList().addAll(nmFnIds);

            log.info("查询NM XN");
            List<MnjxNmXn> existNmXns = iMnjxNmXnService.lambdaQuery().in(MnjxNmXn::getPnrNmId, nmIds).list();
            List<String> nmXnIds = existNmXns.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList());
            nmInfoVo.getNmXnIdList().addAll(nmXnIds);

            log.info("查询NM OI");
            List<MnjxNmOi> existNmOis = iMnjxNmOiService.lambdaQuery().in(MnjxNmOi::getPnrNmId, nmIds).list();
            List<String> nmOiIds = existNmOis.stream().map(MnjxNmOi::getNmOiId).collect(Collectors.toList());
            nmInfoVo.getNmOiIdList().addAll(nmOiIds);

            log.info("查询NM OSI");
            List<MnjxNmOsi> existNmOsis = iMnjxNmOsiService.lambdaQuery().in(MnjxNmOsi::getPnrNmId, nmIds).list();
            List<String> nmOsiIds = existNmOsis.stream().map(MnjxNmOsi::getPnrOsiId).collect(Collectors.toList());
            nmInfoVo.getNmOsiIdList().addAll(nmOsiIds);

            log.info("查询NM RMK");
            List<MnjxNmRmk> existNmRmks = iMnjxNmRmkService.lambdaQuery().in(MnjxNmRmk::getPnrNmId, nmIds).list();
            List<String> nmRmkIds = existNmRmks.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList());
            nmInfoVo.getNmRmkIdList().addAll(nmRmkIds);

            log.info("查询NM SSR");
            List<MnjxNmSsr> existNmSsrs = iMnjxNmSsrService.lambdaQuery().in(MnjxNmSsr::getPnrNmId, nmIds).list();
            List<String> nmSsrIds = existNmSsrs.stream().map(MnjxNmSsr::getNmSsrId).collect(Collectors.toList());
            nmInfoVo.getNmSsrIdList().addAll(nmSsrIds);

            log.info("查询NM FP");
            List<MnjxNmFp> existNmFps = iMnjxNmFpService.lambdaQuery().in(MnjxNmFp::getPnrNmId, nmIds).list();
            List<String> nmFpIds = existNmFps.stream().map(MnjxNmFp::getNmFpId).collect(Collectors.toList());
            nmInfoVo.getNmFpIdList().addAll(nmFpIds);

            log.info("查询LUGGAGE CARRYON");
            List<MnjxLuggageCarryon> existLuggageCarryons = iMnjxLuggageCarryonService.lambdaQuery().in(MnjxLuggageCarryon::getPnrNmId, nmIds).list();
            List<String> luggageCarryonIds = existLuggageCarryons.stream().map(MnjxLuggageCarryon::getLuggageCarryonId).collect(Collectors.toList());
            nmInfoVo.getLuggageCarryonIdList().addAll(luggageCarryonIds);

            log.info("查询LUGGAGE");
            List<MnjxLuggage> existLuggages = iMnjxLuggageService.lambdaQuery().in(MnjxLuggage::getPnrNmId, nmIds).list();
            List<String> luggageIds = existLuggages.stream().map(MnjxLuggage::getLuggageId).collect(Collectors.toList());

            log.info("查询UNPACK INFO");
            List<MnjxUnpackInfo> unpackInfoList = iMnjxUnpackInfoService.lambdaQuery().in(CollUtil.isNotEmpty(luggageIds), MnjxUnpackInfo::getLuggageId, luggageIds).list();
            if (CollUtil.isNotEmpty(unpackInfoList)) {
                List<String> unpackInfoIds = unpackInfoList.stream().map(MnjxUnpackInfo::getUnpackId).collect(Collectors.toList());
                nmInfoVo.getUnpackInfoIdList().addAll(unpackInfoIds);
            }

            nmInfoVo.getLuggageIdList().addAll(luggageIds);

            log.info("查询NM TN");
            List<String> tnIds = new ArrayList<>();
            //成人的TN
            List<MnjxPnrNmTn> existPnrNmTns = iMnjxPnrNmTnService.lambdaQuery().in(MnjxPnrNmTn::getPnrNmId, nmIds).list();
            if (CollUtil.isNotEmpty(existPnrNmTns)) {
                List<String> nmTnIds = existPnrNmTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                tnIds.addAll(nmTnIds);
            }
            //查询婴儿Tn
            if (CollUtil.isNotEmpty(nmXnIds)) {
                List<MnjxPnrNmTn> existXnTns = iMnjxPnrNmTnService.lambdaQuery().in(MnjxPnrNmTn::getNmXnId, nmXnIds).list();
                if (CollUtil.isNotEmpty(existXnTns)) {
                    List<String> xnTnIds = existXnTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                    tnIds.addAll(xnTnIds);
                }
            }
            if (CollUtil.isNotEmpty(tnIds)) {
                log.info("查询NM TICKET");
                List<MnjxPnrNmTicket> existPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getPnrNmTnId, tnIds).list();
                List<String> nmTicketIds = existPnrNmTickets.stream().map(MnjxPnrNmTicket::getNmTicketId).collect(Collectors.toList());
                nmInfoVo.getNmTicketIdList().addAll(nmTicketIds);

                List<String> ticketNoList = existPnrNmTickets.stream().map(MnjxPnrNmTicket::getTicketNo).collect(Collectors.toList());
                log.info("查询TICKET OPERATE RECORD");
                List<MnjxTicketOperateRecord> ticketOperateRecordList = iMnjxTicketOperateRecordService.lambdaQuery().in(CollUtil.isNotEmpty(ticketNoList), MnjxTicketOperateRecord::getTicketNo, ticketNoList).list();
                if (CollUtil.isNotEmpty(ticketOperateRecordList)) {
                    List<String> ticketOperateRecordIds = ticketOperateRecordList.stream().map(MnjxTicketOperateRecord::getTicketOperateRecordId).collect(Collectors.toList());
                    nmInfoVo.getTicketOperateRecordIdList().addAll(ticketOperateRecordIds);
                }

                log.info("查询REFUND TICKET");
                List<MnjxRefundTicket> refundTicketList = iMnjxRefundTicketService.lambdaQuery().in(CollUtil.isNotEmpty(ticketNoList), MnjxRefundTicket::getTicketNo, ticketNoList).list();
                if (CollUtil.isNotEmpty(refundTicketList)) {
                    List<String> refundTicketIds = refundTicketList.stream().map(MnjxRefundTicket::getRefundId).collect(Collectors.toList());
                    nmInfoVo.getRefundTicketIdList().addAll(refundTicketIds);
                }
                nmInfoVo.getTnIdList().addAll(tnIds);
            }
            log.info("查询PSG CKI");
            List<MnjxPsgCki> existPsgCkis = iMnjxPsgCkiService.lambdaQuery().in(MnjxPsgCki::getPnrNmId, nmIds).list();
            if (ObjectUtil.isNotEmpty(existPsgCkis)) {
                List<String> psgCkiIds = existPsgCkis.stream().map(MnjxPsgCki::getPsgCkiId).collect(Collectors.toList());
                log.info("查询EX LUGGAGE");
                List<MnjxExLuggage> existExLuggages = iMnjxExLuggageService.lambdaQuery().in(MnjxExLuggage::getPsgCkiId, psgCkiIds).list();
                List<String> exLuggageIds = existExLuggages.stream().map(MnjxExLuggage::getExLuggageId).collect(Collectors.toList());
                nmInfoVo.getExLuggageIdList().addAll(exLuggageIds);

                log.info("查询PSG CKI OPTION");
                List<MnjxPsgCkiOption> existPsgCkiOptions = iMnjxPsgCkiOptionService.lambdaQuery().in(MnjxPsgCkiOption::getPsgCkiId, psgCkiIds).list();
                List<String> psgCkiOptionIds = existPsgCkiOptions.stream().map(MnjxPsgCkiOption::getPsgCkiOptionId).collect(Collectors.toList());
                nmInfoVo.getPsgCkiOptionIdList().addAll(psgCkiOptionIds);

                log.info("查询PSG SEAT");
                List<MnjxPsgSeat> existPsgSeats = iMnjxPsgSeatService.lambdaQuery().in(MnjxPsgSeat::getPsgCkiId, psgCkiIds).list();
                List<String> psgSeatIds = existPsgSeats.stream().map(MnjxPsgSeat::getPsgSeatId).collect(Collectors.toList());
                nmInfoVo.getPsgSeatIdList().addAll(psgSeatIds);

                log.info("查询PSG OPERATE RECORD");
                List<MnjxPsgOperateRecord> existPsgOperateRecords = iMnjxPsgOperateRecordService.lambdaQuery().in(MnjxPsgOperateRecord::getPsgCkiId, psgCkiIds).list();
                List<String> operateRecordIds = existPsgOperateRecords.stream().map(MnjxPsgOperateRecord::getOperateRecordId).collect(Collectors.toList());
                nmInfoVo.getOperateRecordIdList().addAll(operateRecordIds);
                nmInfoVo.getPsgCkiIdList().addAll(psgCkiIds);

                log.info("查询VERIFY INFO");
                List<MnjxVerifyInfo> verifyInfoList = iMnjxVerifyInfoService.lambdaQuery().in(MnjxVerifyInfo::getPsgCkiId, psgCkiIds).list();
                if (CollUtil.isNotEmpty(verifyInfoList)) {
                    List<String> verifyInfoIds = verifyInfoList.stream().map(MnjxVerifyInfo::getVerifyId).collect(Collectors.toList());
                    nmInfoVo.getVerifyInfoIdList().addAll(verifyInfoIds);
                }
            }
            nmInfoVo.getNmIdList().addAll(nmIds);
        }
    }

//    /**
//     * 删除数据
//     */
//    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
//    public void clearData(SeatVo seatVo, PnrInfoVo pnrInfoVo, NmInfoVo nmInfoVo) {
//        //删除座位
//        this.clearSeat(seatVo);
//        //删除pnr相关信息
//        this.clearPnrInfo(pnrInfoVo, nmInfoVo);
//    }

    /**
     * 删除航班，座位相关数据
     *
     * @param seatVo
     */
    @Override
    public void clearSeat(SeatVo seatVo) {
        log.info("开始清理无人行李表数据");
        this.deleteDate(iMnjxLuggageUnmannedService, seatVo.getLuggageIdList());

        log.info("开始清理座位表数据");
        this.deleteDate(iMnjxSeatService, seatVo.getSeatIdList());

        log.info("开始清理开舱表数据");
        this.deleteDate(iMnjxOpenCabinService, seatVo.getOpenCabinList());

        log.info("开始清理计划航节表数据");
        this.deleteDate(iMnjxPlanSectionService, seatVo.getPlanSectionList());

        log.info("开始清理计划航班表数据");
        this.deleteDate(iMnjxPlanFlightService, seatVo.getPlanFlightIdList());
    }

    /**
     * 删除pnr相关的数据
     */
    @Override
    public void clearPnrInfo(PnrInfoVo pnrInfoVo, NmInfoVo nmInfoVo) {
        log.info("开始清理PNR相关数据");
        log.info("开始清理PNR TK");
        this.deleteDate(iMnjxPnrTkService, pnrInfoVo.getTkList());
        log.info("开始清理PNR SEG");
        this.deleteDate(iMnjxPnrSegService, pnrInfoVo.getPnrSegIdList());
        log.info("开始清理PNR OSI");
        this.deleteDate(iMnjxPnrOsiService, pnrInfoVo.getPnrOsiIdList());
        log.info("开始清理PNR GN");
        this.deleteDate(iMnjxPnrGnService, pnrInfoVo.getPnrGnIdList());
        log.info("开始清理PNR FC");
        this.deleteDate(iMnjxPnrFcService, pnrInfoVo.getPnrFcIdList());
        log.info("开始清理PNR EI");
        this.deleteDate(iMnjxPnrEiService, pnrInfoVo.getPnrEiIdList());
        log.info("开始清理PNR TC");
        this.deleteDate(iMnjxPnrTcService, pnrInfoVo.getPnrTcIdList());
        log.info("开始清理PNR AT");
        this.deleteDate(iMnjxPnrAtService, pnrInfoVo.getPnrAtList());
        log.info("开始清理PNR FN");
        this.deleteDate(iMnjxPnrFnService, pnrInfoVo.getPnrFnIdList());
        log.info("开始清理PNR FP");
        this.deleteDate(iMnjxPnrFpService, pnrInfoVo.getPnrFpIdList());
        log.info("开始清理PNR RECORD");
        this.deleteDate(iMnjxPnrRecordService, pnrInfoVo.getPnrRecordIdList());
        log.info("开始清理PNR RMK");
        this.deleteDate(iMnjxPnrRmkService, pnrInfoVo.getPnrRmkIdList());
        log.info("开始清理PNR CT");
        this.deleteDate(iMnjxPnrCtService, pnrInfoVo.getPnrCtIdList());
        log.info("开始清理关注的PNR");
        this.deleteDate(iMnjxPnrSubService, pnrInfoVo.getPnrSubIdList());
        log.info("开始清理MENT");
        this.deleteDate(iMnjxMentService, pnrInfoVo.getIdBgtList());
        //删除nm
        log.info("开始清理PNR旅客相关数据");
        this.clearNmInfo(nmInfoVo);
        this.deleteDate(iMnjxPnrService, pnrInfoVo.getPnrIdsList());
    }

    /**
     * 删除NM旅客相关的数据
     */
    private void clearNmInfo(NmInfoVo nmInfoVo) {
        log.info("开始清理NM FC");
        this.deleteDate(iMnjxNmFcService, nmInfoVo.getNmFcIdList());
        log.info("开始清理NM EI");
        this.deleteDate(iMnjxNmEiService, nmInfoVo.getNmEiIdLIst());
        log.info("开始清理NM CT");
        this.deleteDate(iMnjxNmCtService, nmInfoVo.getNmCtIdList());
        log.info("开始清理NM UM");
        this.deleteDate(iMnjxPnrNmUmService, nmInfoVo.getNmUmIdList());
        log.info("开始清理NM FN");
        this.deleteDate(iMnjxNmFnService, nmInfoVo.getNmFnIdList());
        log.info("开始清理NM XN");
        this.deleteDate(iMnjxNmXnService, nmInfoVo.getNmXnIdList());
        log.info("开始清理NM OI");
        this.deleteDate(iMnjxNmOiService, nmInfoVo.getNmOiIdList());
        log.info("开始清理NM OSI");
        this.deleteDate(iMnjxNmOsiService, nmInfoVo.getNmOsiIdList());
        log.info("开始清理NM RMK");
        this.deleteDate(iMnjxNmRmkService, nmInfoVo.getNmRmkIdList());
        log.info("开始清理NM SSR");
        this.deleteDate(iMnjxNmSsrService, nmInfoVo.getNmSsrIdList());
        log.info("开始清理NM FP");
        this.deleteDate(iMnjxNmFpService, nmInfoVo.getNmFpIdList());
        log.info("开始清理LUGGAGE CARRYON");
        this.deleteDate(iMnjxLuggageCarryonService, nmInfoVo.getLuggageCarryonIdList());
        log.info("开始清理UNPACK INFO");
        this.deleteDate(iMnjxUnpackInfoService, nmInfoVo.getUnpackInfoIdList());
        log.info("开始清理LUGGAGE");
        this.deleteDate(iMnjxLuggageService, nmInfoVo.getLuggageIdList());
        log.info("开始清理NM TICKET");
        this.deleteDate(iMnjxPnrNmTicketService, nmInfoVo.getNmTicketIdList());
        log.info("开始清理TICKET OPERATE RECORD");
        this.deleteDate(iMnjxTicketOperateRecordService, nmInfoVo.getTicketOperateRecordIdList());
        log.info("开始清理REFUND TICKET");
        this.deleteDate(iMnjxRefundTicketService, nmInfoVo.getRefundTicketIdList());
        log.info("开始清理NM TN");
        this.deleteDate(iMnjxPnrNmTnService, nmInfoVo.getTnIdList());
        log.info("开始清理EX LUGGAGE");
        this.deleteDate(iMnjxExLuggageService, nmInfoVo.getExLuggageIdList());
        log.info("开始清理PSG CKI OPTION");
        this.deleteDate(iMnjxPsgCkiOptionService, nmInfoVo.getPsgCkiOptionIdList());
        log.info("开始清理PSG SEAT");
        this.deleteDate(iMnjxPsgSeatService, nmInfoVo.getPsgSeatIdList());
        log.info("开始清理PSG OPERATE RECORD");
        this.deleteDate(iMnjxPsgOperateRecordService, nmInfoVo.getOperateRecordIdList());
        log.info("开始清理PSG CKI");
        this.deleteDate(iMnjxPsgCkiService, nmInfoVo.getPsgCkiIdList());
        log.info("开始清理VERIFY INFO");
        this.deleteDate(iMnjxVerifyInfoService, nmInfoVo.getVerifyInfoIdList());
        log.info("开始清理NM");
        this.deleteDate(iMnjxPnrNmService, nmInfoVo.getNmIdList());
    }
}
