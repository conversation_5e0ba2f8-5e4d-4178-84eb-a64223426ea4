<job id="createKeyStream">
	<script language="VBScript" src="util.vbs" />
	<script language="VBScript" src="regUtil.vbs" />
	<script language="VBScript">

		CheckZeroArgs("usage: cscript regCreateKey.wsf architecture")
		DetermineOSArchitecture()
		LoadRegistryImplementationByOSArchitecture()

		Do While Not stdin.AtEndOfLine
			strLine = stdin.ReadLine()			
			strLine = unescape(trim(strLine))

			If IsNull(strLine) or strLine = "" Then
				WScript.Quit 25127
			End If
			
			ParseHiveAndSubKey strLine, constHive, strSubKey

			if Is<PERSON>ull(constHive) Then
				WriteLineErr "unsupported hive " & strLine
				WScript.Quit 25122
			End If

			Result = CreateKey(constHive, strSubKey)

			If Not Result = 0 Then				
				WScript.Quit Result
			End If
		Loop
	</script>
</job>