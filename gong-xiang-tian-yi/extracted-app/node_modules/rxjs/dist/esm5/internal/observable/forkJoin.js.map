{"version": 3, "file": "forkJoin.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AAC3E,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAyIpD,MAAM,UAAU,QAAQ;IAAC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IACrC,IAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzC,IAAA,KAA0B,oBAAoB,CAAC,IAAI,CAAC,EAA5C,OAAO,UAAA,EAAE,IAAI,UAA+B,CAAC;IAC3D,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAC,UAAU;QAC/B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;QAC3B,IAAI,CAAC,MAAM,EAAE;YACX,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,oBAAoB,GAAG,MAAM,CAAC;QAClC,IAAI,kBAAkB,GAAG,MAAM,CAAC;gCACvB,WAAW;YAClB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CACvC,wBAAwB,CACtB,UAAU,EACV,UAAC,KAAK;gBACJ,IAAI,CAAC,QAAQ,EAAE;oBACb,QAAQ,GAAG,IAAI,CAAC;oBAChB,kBAAkB,EAAE,CAAC;iBACtB;gBACD,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC,EACD,cAAM,OAAA,oBAAoB,EAAE,EAAtB,CAAsB,EAC5B,SAAS,EACT;gBACE,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE;oBACtC,IAAI,CAAC,kBAAkB,EAAE;wBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;qBAC7D;oBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACvB;YACH,CAAC,CACF,CACF,CAAC;;QAvBJ,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,EAAE,WAAW,EAAE;oBAApD,WAAW;SAwBnB;IACH,CAAC,CAAC,CAAC;IACH,OAAO,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACjF,CAAC"}